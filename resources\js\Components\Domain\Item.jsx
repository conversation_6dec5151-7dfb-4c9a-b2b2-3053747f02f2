import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import { getEventValue } from "@/Util/TargetInputEvent";
import useOutsideClick from "@/Util/useOutsideClick";
import { Link, router, usePage } from "@inertiajs/react";
import { useRef, useState } from "react";
import { BsDatabaseFillLock } from "react-icons/bs";

//* COMPONENTS
import AppVerificationPromptGroup from "@/Components/App/AppVerificationPromptGroupComponent";

import {
    MdInfoOutline,
    MdLockOpen,
    MdMoreVert,
    MdOutlineEdit,
    MdOutlineFlag,
    MdOutlineLock,
    MdLockPerson,
} from "react-icons/md";

import { TbTrash, TbTrashOff } from "react-icons/tb";

import _DomainStatus from "@/Constant/_DomainStatus";
import convertToTitleCase from "@/Util/convertToTitleCase";
import getRecentTime from "@/Util/getRecentTime";
import getRemainingTime from "@/Util/getRemainingTime";
import setDefaultDateFormat from "../../Util/setDefaultDateFormat";
import ShowMoreLess from "../ShowMoreLess";
import { StatusBadge } from "./StatusBadge";

import Modal from "@/Components/Modal";
import SecondaryButton from "../SecondaryButton";
import DangerButton from "../DangerButton";

//* UTILS
import UtilCheckIfHasSecuredTransaction from "@/Util/UtilCheckIfHasSecuredTransaction";

export default function DomainItems({
    index,
    item,
    isSelected,
    dateTimeFormat,
    onCheckboxChange,
    onRequestAuthcodeClicked,
}) {
    const isExpired = item.status.toLowerCase() === "expired";
    const isRedemption = item.status.toLowerCase() === "redemption";
    const user = usePage().props.auth.user;
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    // const { errors } = usePage().props;

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, item, getEventValue(e));
    };

    const onHandleOnClick = (link, method, data) => {
        setShow(false);
        router.visit(link, {
            method: method,
            data: data,
            replace: true,
            preserveState: false,
        });
    };

    const handleRestoreClick = () => {
        setShow(false);
        router.post(
            route("domain.redeem.confirm"),
            {
                user_id: user.id,
                domains: [item],
            },
            {
                replace: true,
                preserveState: false,
            }
        );
    };

    console.log(item);

    const onChangeDateTimeFormat = (format, time) => {
        switch (format) {
            case 1:
                return <span>{setDefaultDateFormat(time)}</span>;
            case 2:
                if (Date.now() > time)
                    return (
                        <span className="text-red-500">
                            {getRecentTime(time)}
                        </span>
                    );
                return (
                    <span className="text-green-500 font-bold">
                        {getRemainingTime(time)}
                    </span>
                );
            default:
                return <span>{setDefaultDateFormat(time)}</span>;
        }
    };

    const isLockedIn = (date) => {
        const today = new Date();
        var lockInDate = new Date(date * 1000);

        if (today < lockInDate) return true;

        return false;
    };

    const convertDate = (date) => {
        var lockInDate = new Date(date * 1000);

        return lockInDate.toDateString().split(" ").slice(1).join(" ");
    };

    const getNameserversLength = (nameservers) => {
        if (!nameservers) return 0;

        let jsonNS = JSON.parse(nameservers);
        if (!jsonNS) return 0;
        return jsonNS.length;
    };

    const getNameserversNames = (nameservers) => {
        if (!nameservers) return "";

        let jsonNS = JSON.parse(nameservers);
        if (!jsonNS) return "";
        return jsonNS.join(", ");
    };

    // console.log(item);

    const toolTipClassName =
        "absolute bottom-full left-1/2 -translate-x-1/2 mb-2 bg-primary text-white text-sm rounded px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-10 shadow-lg w-max";

    const [domain, setDomain] = useState("");
    const [reason, setReason] = useState("");
    const [agreePolicy, setAgreePolicy] = useState(false);
    const [agreeGrace, setAgreeGrace] = useState(false);
    const [error, setError] = useState("");
    const [confirmingUserDeletion, setConfirmingUserDeletion] = useState(false);
    const closeModal = () => {
        setConfirmingUserDeletion(false);
        setDomain("");
        setReason("");
        setAgreePolicy(false);
        setAgreeGrace(false);
    };

    //! STATES
    const [stateShowVerificationPrompt, setStateShowVerificationPrompt] =
        useState(false);

    //! VARIABLES
    const shouldVerifyUser = UtilCheckIfHasSecuredTransaction("domainDelete");

    const clickDeleteBtn = () => {
        if (shouldVerifyUser == true) {
            setStateShowVerificationPrompt(true);
        } else {
            handleOnSubmit();
        }
    };

    const [errors, setErrors] = useState({});

    const handleDeleteDomainRequest = (itemReq) => {
        axios
            .post(route("domain.cancel"), {
                ...itemReq,
            })
            .then((response) => {
                // Clear fields
                setDomain("");
                setReason("");
                setAgreePolicy(false);
                setAgreeGrace(false);
                closeModal();
                router.visit(route("domain.cancellation.success"));
                return response;
            })
            .catch((error) => {
                if (error.response?.status === 422) {
                    setErrors(error.response.data.errors);
                } else {
                    console.log("Something went wrong.");
                }
                return error.response;
            });
    };

    function handleOnSubmit() {
        setConfirmingUserDeletion(true);
    }

    const handleConfirm = () => {
        handleDeleteDomainRequest({
            domain,
            domainId: item.id,
            userID: item.user_contact_user_id,
            reason,
            agree_policy: agreePolicy,
            agree_grace: agreeGrace,
        });
    };
    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    {["active", "expired", "redemption"].includes(
                        item.status.toLowerCase()
                    ) ? (
                        <>
                            <Checkbox
                                checked={isSelected}
                                disabled={false}
                                handleChange={handleCheckboxChange}
                            />
                            <Link
                                href={route("domain.view", { id: item.id })}
                                method="get"
                                as="button"
                                type="button"
                            >
                                <span className="text-link cursor-pointer">
                                    {item.name}
                                </span>
                            </Link>
                        </>
                    ) : (
                        <>
                            <div className="opacity-0 pointer-events-none">
                                <Checkbox disabled={true} />
                            </div>
                            <span>{item.name}</span>
                        </>
                    )}
                </label>
            </td>
            <td>
                <ShowMoreLess
                    condition={
                        getNameserversNames(item.nameservers).length > 15
                    }
                    item={getNameserversNames(item.nameservers)}
                />
            </td>
            <td>
                <span>{item.user_category_name}</span>
            </td>
            <td>
                <div className="inline-flex justify-between">
                    {item.client_status &&
                    item.client_status.includes(
                        _DomainStatus.STATUS.TRANSFER
                    ) ? (
                        isLockedIn(item.locked_until) ? (
                            // <div className="relative group inline-block cursor-pointer">
                            //     {/* Lock Icon */}
                            //     <MdOutlineLock className="text-danger" />

                            //     {/* Custom Tooltip on Hover */}
                            //     <div className={toolTipClassName}>
                            //         Locked until{" "}
                            //         {convertDate(item.locked_until)} <br />
                            //     </div>
                            // </div>
                            <div className="relative group inline-block tooltip-arrow">
                                <MdOutlineLock className="text-danger" />
                                <div className={toolTipClassName}>
                                    <div className="flex items-start gap-2">
                                        {/* Info Icon */}
                                        <MdInfoOutline className="text-blue-300 mt-0.5" />

                                        {/* Text Content */}
                                        <div>
                                            <div>Transfer Lock</div>
                                            <div className="text-xs text-gray-300 mt-1">
                                                Until{" "}
                                                {convertDate(item.locked_until)}
                                                , and cannot be reassigned or
                                                transferred.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            // <span title="Transfer Lock">
                            //     <MdOutlineLock className="text-danger" />
                            // </span>
                            <div className="relative group inline-block tooltip-arrow">
                                <MdOutlineLock className="text-danger" />
                                <div className={toolTipClassName}>
                                    <div className="flex items-start gap-2">
                                        {/* Info Icon */}
                                        <MdInfoOutline className="text-blue-300 mt-0.5" />

                                        {/* Text Content */}
                                        <div>
                                            <div>Transfer Lock</div>
                                            <div className="text-xs text-gray-300 mt-1">
                                                Cannot be reassigned or
                                                transferred.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )
                    ) : (
                        // <span title="Transfer Unlock">
                        //     <MdLockOpen className="text-success" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <MdLockOpen className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-blue-300 mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Transfer Unlocked</div>
                                        <div className="text-xs text-gray-300 mt-1">
                                            Can now be reassigned or
                                            transferred.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.UPDATE) ? (
                        // <span title="Update Disabled">
                        //     <MdOutlineEditOff className="text-danger" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <MdLockOpen className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-blue-300 mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Update Disabled</div>
                                        <div className="text-xs text-gray-300 mt-1">
                                            Locked and cannot be modified at
                                            this time.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // <span title="Update Enabled">
                        //     <MdOutlineEdit className="text-success" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <MdOutlineEdit className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Update Enabled</div>
                                        <div className="text-xs text-white mt-1">
                                            Unlocked and enabled for editing.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.HOLD) ? (
                        // <span title="On Hold">
                        //     <MdOutlineFlag className="text-danger" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <MdOutlineFlag className="text-danger" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>On Hold</div>
                                        <div className="text-xs text-white mt-1">
                                            Domain access and features are
                                            temporarily disabled.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // <span title="Active">
                        //     <MdOutlineFlag className="text-success" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <MdOutlineFlag className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Active</div>
                                        <div className="text-xs text-white mt-1">
                                            Verified and ready for use.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.DELETE) ? (
                        // <span title="Delete Disabled">
                        //     <TbTrashOff className="text-danger" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <TbTrashOff className="text-danger" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Delete Disabled</div>
                                        <div className="text-xs text-white mt-1">
                                            Deletion is locked or restricted.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // <span title="Delete Enabled">
                        //     <TbTrash className="text-success" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <TbTrash className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Delete Enabled</div>
                                        <div className="text-xs text-white mt-1">
                                            You can delete this domain if
                                            needed.
                                            <br />
                                            Please proceed with caution.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {item.client_status &&
                    item.client_status.includes(_DomainStatus.STATUS.RENEW) ? (
                        // <span title="Renew Disabled">
                        //     <MdPauseCircleOutline className="text-danger" />
                        // </span>
                        <div className="relative group inline-block tooltip-arrow">
                            <TbTrash className="text-success" />
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    {/* Info Icon */}
                                    <MdInfoOutline className="text-white mt-0.5" />

                                    {/* Text Content */}
                                    <div>
                                        <div>Renew Disabled</div>
                                        <div className="text-xs text-white mt-1">
                                            Domain's registry to reject requests
                                            to renew your domain. <br />
                                            It is an uncommon status that is
                                            usually enacted during legal
                                            disputes or when your domain is
                                            subject to deletion.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="relative group inline-block tooltip-arrow">
                            {item.client_status && item.client_status.includes(
                                _DomainStatus.STATUS.HOLD
                            ) ? (
                                <BsDatabaseFillLock className="text-red-400 ml-0.5" />
                            ) : (
                                <BsDatabaseFillLock className="text-success ml-0.5" />
                            )}
                            <div className={toolTipClassName}>
                                <div className="flex items-start gap-2">
                                    <MdInfoOutline className="text-white mt-0.5" />
                                    <div>
                                        <div>Client Hold Status</div>
                                        <div className="text-xs text-white mt-1">
                                            {item.client_status && item.client_status.includes(
                                                _DomainStatus.STATUS.HOLD
                                            )
                                                ? "Domain is currently on Client Hold."
                                                : "Domain is not on Client Hold"}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </td>
            <td>
                <div className="inline-flex justify-between">
                    {/* <span>
                        <MdOutlineLock className="text-primary" />
                    </span> */}
                    {/* <div className="relative group inline-block cursor-pointer">
                        <MdOutlineLock className="text-primary" />
                        <div className={toolTipClassName}>
                            <div className="flex items-start gap-2">
                                <MdInfoOutline className="text-white mt-0.5" />
                                <div>
                                    <div>WHOIS Lock Enabled</div>
                                    <div className="text-xs text-white mt-1">
                                        Prevents unauthorized changes to domain
                                        ownership or settings.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> */}
                    <div className="relative group inline-block cursor-pointer">
                        <Link
                            href={route("whois.search", {
                                domain: item.name,
                            })}
                            method="get"
                            as="button"
                            type="button"
                        >
                            <MdLockPerson
                                className={
                                    item.privacy_protection
                                        ? "text-success"
                                        : "text-danger"
                                }
                            />
                        </Link>
                        <div className={toolTipClassName}>
                            <div className="flex items-start gap-2">
                                <MdInfoOutline className="text-white mt-0.5" />
                                <div>
                                    <div>
                                        {item.privacy_protection
                                            ? "Privacy Protection Enabled"
                                            : "Privacy Protection Disabled"}
                                    </div>
                                    <div className="text-xs text-white mt-1">
                                        {item.privacy_protection ? (
                                            <>
                                                Your personal information
                                                <br />
                                                is protected from public WHOIS
                                                lookup.
                                            </>
                                        ) : (
                                            <>
                                                Your domain registration
                                                information
                                                <br />
                                                is publicly visible.
                                            </>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/* <div className="relative group inline-block cursor-pointer">
                        <MdLockPerson className={item.privacy_protection ? "text-success" : "text-danger"} />
                        <div className={toolTipClassName}>
                            <div className="flex items-start gap-2"> */}
                    {/* Info Icon */}
                    {/* <MdInfoOutline className="text-white mt-0.5" /> */}

                    {/* Text Content */}
                    {/* <div>
                                    <div>{item.privacy_protection ? "Privacy Protection Enabled" : "Privacy Protection Disabled"}</div>
                                    <div className="text-xs text-white mt-1">
                                        {item.privacy_protection 
                                            ? "Your personal information is protected from public WHOIS lookup." 
                                            : "Your domain registration information is publicly visible."}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> */}
                    {/* <Link
                        href={route("whois.search", { domain: item.name })}
                        method="get"
                        as="button"
                        type="button"
                    >
                        <span title="Go to Link">
                            <MdLink className="text-success cursor-pointer" />
                        </span>
                    </Link> */}
                    {/* <div className="relative group inline-block">
                        <div className={toolTipClassName}>
                            <div className="flex items-start gap-2 ">
                                <MdInfoOutline className="text-white mt-0.5" />
                                <div className="">
                                    <div>Go to Link</div>
                                    <div className="text-xs text-white mt-1">
                                        View the public domain registration
                                        record.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> */}
                </div>
            </td>
            <td>{onChangeDateTimeFormat(dateTimeFormat, item.expiry)}</td>
            <td>{setDefaultDateFormat(item.created_at)}</td>
            <td>
                <span>
                    {<StatusBadge status={convertToTitleCase(item.status)} />}
                </span>
            </td>
            <td>
                {["active", "expired", "redemption"].includes(
                    item.status.toLowerCase()
                ) && (
                    <span ref={ref} className="relative">
                        <button
                            className="flex items-center"
                            onClick={() => setShow(!show)}
                        >
                            <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                        </button>
                        <DropDownContainer show={show}>
                            {isRedemption ? (
                                <>
                                    <button
                                        className="hover:bg-gray-100 px-5 py-1 justify-start flex text-blue-500"
                                        onClick={handleRestoreClick}
                                    >
                                        Restore
                                    </button>
                                    <button
                                        className="hover:bg-gray-100 px-5 py-1 justify-start flex text-red-500"
                                        onClick={() => clickDeleteBtn()}
                                    >
                                        Delete
                                    </button>
                                </>
                            ) : item.client_status &&
                              item.client_status.includes(
                                  _DomainStatus.STATUS.HOLD
                              ) ? (
                                <button
                                    disabled={isExpired}
                                    className={`hover:bg-gray-100 px-5 py-1 justify-start flex items-center gap-2 ${
                                        isExpired && "text-gray-200"
                                    }`}
                                    onClick={() =>
                                        onHandleOnClick(
                                            route("whois.search"),
                                            "get",
                                            {
                                                domain: item.name,
                                            }
                                        )
                                    }
                                >
                                    Get Whois Privacy
                                </button>
                            ) : (
                                <>
                                    <button
                                        disabled={
                                            (item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )) ||
                                            isExpired
                                        }
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                            ((item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )) ||
                                                isExpired) &&
                                            "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("domain.lock.confirm"),
                                                "post",
                                                {
                                                    user_id: user.id,
                                                    domains: [item],
                                                    domains_list: [item.id],
                                                    isDisable: true,
                                                }
                                            )
                                        }
                                    >
                                        Lock
                                    </button>
                                    <button
                                        disabled={
                                            !(
                                                item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )
                                            ) || isExpired
                                        }
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                            (!(
                                                item.client_status &&
                                                item.client_status.includes(
                                                    _DomainStatus.STATUS
                                                        .TRANSFER
                                                )
                                            ) ||
                                                isExpired) &&
                                            "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("domain.lock.confirm"),
                                                "post",
                                                {
                                                    user_id: user.id,
                                                    domains: [item],
                                                    domains_list: [item.id],
                                                    isDisable: false,
                                                }
                                            )
                                        }
                                    >
                                        Unlock
                                    </button>
                                    <button
                                        disabled={isExpired}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex items-center gap-2 ${
                                            isExpired && "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("whois.search"),
                                                "get",
                                                {
                                                    domain: item.name,
                                                }
                                            )
                                        }
                                    >
                                        Get Whois Privacy
                                    </button>
                                    <button
                                        disabled={isExpired}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex items-center gap-2 ${
                                            isExpired && "text-gray-200"
                                        }`}
                                        onClick={() => {
                                            setShow(false);
                                            router.post(
                                                route(
                                                    "domain.privacy.toggle",
                                                    item.id
                                                ),
                                                {
                                                    user_id: user.id,
                                                    privacy:
                                                        !item.privacy_protection,
                                                }
                                            );
                                        }}
                                    >
                                        <>
                                            <span>
                                                {item.privacy_protection
                                                    ? "Disable Privacy"
                                                    : "Enable Privacy"}
                                            </span>
                                        </>
                                    </button>
                                    <button
                                        disabled={false}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex`}
                                        onClick={() =>
                                            onHandleOnClick(
                                                route("domain.renew.confirm"),
                                                "post",
                                                {
                                                    user_id: user.id,
                                                    domains: [item],
                                                }
                                            )
                                        }
                                    >
                                        Renew Now
                                    </button>

                                    <button
                                        disabled={isExpired}
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex ${
                                            isExpired && "text-gray-200"
                                        }`}
                                        onClick={() =>
                                            onRequestAuthcodeClicked([item])
                                        }
                                    >
                                        Request Auth Code
                                    </button>

                                    <button
                                        className={`hover:bg-gray-100 px-5 py-1 justify-start flex text-red-500`}
                                        onClick={() => clickDeleteBtn()}
                                    >
                                        Delete
                                    </button>
                                </>
                            )}
                        </DropDownContainer>

                        <AppVerificationPromptGroup
                            isShow={stateShowVerificationPrompt}
                            onClose={() =>
                                setStateShowVerificationPrompt(false)
                            }
                            onSubmitSuccess={handleOnSubmit}
                            onSubmitError={() => {}}
                        />

                        <Modal
                            show={confirmingUserDeletion}
                            onClose={closeModal}
                            maxWidth="3xl"
                        >
                            <div className="flex flex-col px-10 pt-5 pb-10 gap-y-6">
                                <section className="flex flex-col gap-2 pb-4 border-b border-gray-200">
                                    <div className="text-lg text-primary font-bold">
                                        Delete Domain: {item.name}
                                    </div>
                                    <div className="text-gray-600">
                                        To delete {item.name}, please provide a
                                        detailed reason for our review.
                                    </div>
                                </section>
                                <section className="flex flex-col gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Type <strong>{item.name}</strong> to
                                            confirm{" "}
                                            <span className="text-red-500">
                                                *
                                            </span>
                                        </label>
                                        <input
                                            type="text"
                                            className="w-full border border-gray-300 rounded-md p-3 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                                            value={domain}
                                            onChange={(e) =>
                                                setDomain(e.target.value)
                                            }
                                            name="domain"
                                            placeholder={`Type "${item.name}" here`}
                                        />
                                        {errors.domain && (
                                            <p className="text-sm text-red-600 mt-1">
                                                {errors.domain}
                                            </p>
                                        )}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Your Message / Reason for Deletion{" "}
                                            <span className="text-red-500">
                                                *
                                            </span>
                                        </label>
                                        <textarea
                                            className="w-full border border-gray-300 rounded-md p-3 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                                            rows="4"
                                            value={reason}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                if (value.length <= 500) {
                                                    setReason(value);
                                                }
                                            }}
                                            maxLength={500}
                                            placeholder="e.g., I no longer need this domain for my business..."
                                            name="reason"
                                        ></textarea>

                                        {errors.reason && (
                                            <p className="text-sm text-red-600 mt-1">
                                                {errors.reason}
                                            </p>
                                        )}

                                        <p className="text-xs text-gray-500 mt-1">
                                            {reason.length}/500 characters
                                        </p>
                                    </div>

                                    <div className="space-y-3">
                                        <label className="flex items-start space-x-3">
                                            <Checkbox
                                                name="domainCancellationPolicy"
                                                value="agree_policy"
                                                checked={agreePolicy}
                                                handleChange={() =>
                                                    setAgreePolicy(!agreePolicy)
                                                }
                                            />
                                            <span className="text-sm text-gray-700">
                                                I have read and agree to the{" "}
                                                <a
                                                    href="/policy/domain-cancellation"
                                                    target="_blank"
                                                    className="text-link underline hover:text-primary"
                                                >
                                                    Domain Cancellation Policy
                                                </a>
                                                .
                                            </span>
                                        </label>
                                        {errors.agree_policy && (
                                            <p className="text-sm text-red-600 ml-7">
                                                {errors.agree_policy}
                                            </p>
                                        )}

                                        <label className="flex items-start space-x-3">
                                            <Checkbox
                                                name="FiveDayPeriod"
                                                value="agree_grace"
                                                checked={agreeGrace}
                                                handleChange={() =>
                                                    setAgreeGrace(!agreeGrace)
                                                }
                                            />
                                            <span className="text-sm text-gray-700">
                                                I understand the{" "}
                                                <a
                                                    href="/policy/5-days-grace"
                                                    target="_blank"
                                                    className="text-link underline hover:text-primary"
                                                >
                                                    5-Day Grace Period Policy
                                                </a>
                                                .
                                            </span>
                                        </label>
                                        {errors.agree_grace && (
                                            <p className="text-sm text-red-600 ml-7">
                                                {errors.agree_grace}
                                            </p>
                                        )}
                                    </div>

                                    {error && (
                                        <div className="bg-red-50 border border-red-200 rounded-md p-3">
                                            <p className="text-sm text-red-600">
                                                {error}
                                            </p>
                                        </div>
                                    )}
                                </section>


                                <section className="flex justify-between items-center pt-4 border-t border-gray-200">
                                    <div className="text-sm text-gray-600">
                                        <div className="font-medium">
                                            Need help?
                                        </div>
                                        <div><EMAIL></div>
                                        <div>+63 912 345 6789</div>
                                    </div>

                                    <div className="flex gap-3">
                                        <SecondaryButton onClick={closeModal}>
                                            Cancel
                                        </SecondaryButton>
                                        <DangerButton onClick={handleConfirm}>
                                            Delete Domain
                                        </DangerButton>
                                    </div>
                                </section>
                            </div>
                        </Modal>
                    </span>
                )}
            </td>
        </tr>
    );
}
