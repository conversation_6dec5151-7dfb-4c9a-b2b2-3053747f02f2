<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Events\DomainHistoryEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Models\RegisteredDomain;
use App\Modules\Domain\Services\UpdateServices\RestoreReportPayload;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Payment\Services\PaymentNodeService;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use Carbon\Carbon;
use App\Util\Constant\QueueErrorTypes;
use Exception;

class JobRedemptionEppService
{
    use UserLoggerTrait;

    private $dispatchDelayInSeconds = 180;
    private JobRecord $jobRecord;
    private const COL_STATUS = 'status';

    public static function instance(): self
    {
        return new self;
    }

    public function handle(JobRecord $record): void
    {
        $this->jobRecord = $record;
        app(AuthLogger::class)->info($this->fromWho("Domain redemption started: {$this->jobRecord->name}", $this->jobRecord->email));

        $this->dispatchEppJobs();
    }

    private function dispatchEppJobs(): void
    {
        $restoreResponse = $this->callEppDomainRestoreRequest();
        $this->evaluateResponse($restoreResponse, 'Restore Request');

        $eppInfoResponse = $this->callEppInfo();
        $this->evaluateResponse($eppInfoResponse, 'EPP Info');

        $reportResponse = $this->callEppDomainRestoreReport(eppInfoData: $eppInfoResponse);
        $this->evaluateResponse($reportResponse, 'Restore Report');

        // $eppInfoResponse = $this->callEppInfo();
        // $this->evaluateResponse($eppInfoResponse, 'EPP Info');

        $restoreAcceptedResponse = $this->callEppRestoreAccepted();
        $this->evaluateResponse($restoreAcceptedResponse, 'EPP Restore Accepted');

        $renewResponse = $this->callEppRenewDomain();
        $this->evaluateResponse($renewResponse, 'Domain Renewal');

        $this->handleSuccess();
    }

    private function handleSuccess(): bool
    {
        $this->jobRecord->stopJobRetry(DomainStatus::ACTIVE);
        $this->updateDomainStatus();
        $this->updateRegisteredDomainStatus();
        $this->updatePaymentNodeStatus();
        $this->sendNotification();

        JobDispatchService::instance()->refreshEppDispatch($this->jobRecord->getRecord(), $this->dispatchDelayInSeconds);

        event(new DomainHistoryEvent([
            'domain_id' => $this->jobRecord->domainId,
            'type' => 'DOMAIN_REDEMPTION',
            'user_id' => $this->jobRecord->userId,
            'status' => 'success',
            'message' => 'Domain "' . $this->jobRecord->name . '" redemption completed successfully by ' . $this->jobRecord->email . '.',
            'payload' => $this->jobRecord,
        ]));

        return true;
    }

    private function callEppRestoreAccepted(): array
    {
        return EppDomainService::instance()->callEppRestoreAccepted($this->jobRecord->name);
    }

    private function callEppDomainRestoreRequest(): array
    {
        return EppDomainService::instance()->callEppDomainRestoreRequest($this->jobRecord->name);
    }

    private function callEppInfo(): array
    {
        return EppDomainService::instance()->callEppInfo($this->jobRecord->name);
    }

    private function callEppDomainRestoreReport(array $eppInfoData): array
    {
        $payload = new RestoreReportPayload($this->jobRecord->name, $this->jobRecord->email, $eppInfoData);
        return EppDomainService::instance()->callEppDomainRestoreReport($payload->toArray());
    }

    private function callEppRenewDomain(): array
    {
        return EppDomainService::instance()->renewDomain($this->jobRecord->domain, $this->jobRecord->email);
    }

    private function evaluateResponse(array $response, string $operation): void
    {
        if (!$this->isEppResponseSuccessful($response)) {
            $errorDetails = $this->extractErrorDetails(response: $response);
            $errorMessage = "{$operation} failed: {$errorDetails}";
            app(AuthLogger::class)->error($this->fromWho($errorMessage, $this->jobRecord->email));

            if ($this->isRetryableError($response)) {
                throw new Exception(QueueErrorTypes::RETRY);
            }

            if (isset($response['eppCode'])) {
                app(AuthLogger::class)->info($this->fromWho("Non-retryable EPP error {$response['eppCode']}, processing refund immediately", $this->jobRecord->email));
                $this->processRefund("EPP Error {$response['eppCode']}: {$errorDetails}");
            }

            throw new Exception($errorMessage);
        }
    }

    private function isRetryableError(array $response): bool
    {
        if (isset($response['eppCode'])) {
            $retryableCodes = ['2400', '2500', '2502'];
            return in_array($response['eppCode'], $retryableCodes);
        }
        return true;
    }

    private function extractErrorDetails(array $response): string
    {
        if (isset($response['status']['message'])) {
            $message = $response['status']['message'];
            $eppCode = $response['status']['eppCode'] ?? 'Unknown';
            return "EPP Error {$eppCode}: {$message}";
        }

        if (isset($response['errors'])) {
            return is_array($response['errors']) ? implode(', ', $response['errors']) : $response['errors'];
        }

        return json_encode($response);
    }

    private function isEppResponseSuccessful(array $response): bool
    {
        if (isset($response['status'])) {
            if ($response['status'] === 'OK' || $response['status'] === 'success') {
                return true;
            }

            if (is_array($response['status'])) {
                $statusCode = $response['status']['statusCode'] ?? null;
                $status = $response['status']['status'] ?? null;

                if (in_array($statusCode, [200, 1000, 1001])) {
                    return true;
                }

                if (in_array($status, ['FORBIDDEN', 'ERROR', 'FAILED'])) {
                    return false;
                }
            }
        }

        if (isset($response['statusCode']) && in_array($response['statusCode'], [200, 1000, 1001])) {
            return true;
        }

        if (!isset($response['errors']) && !isset($response['error']) && !isset($response['status']['message'])) {
            return true;
        }

        return false;
    }

    private function updateDomainStatus(): void
    {
        DomainService::instance()->updateDomainStatus($this->jobRecord->domainId, DomainStatus::ACTIVE, false, $this->jobRecord->email);
    }

    private function updateRegisteredDomainStatus(): void
    {
        $now = Carbon::now();

        RegisteredDomain::where('id', $this->jobRecord->registeredDomainId)
            ->update([
                'status' => UserDomainStatus::OWNED,
                'deleted_at' => null,
                'updated_at' => $now,
            ]);

        app(AuthLogger::class)->info($this->fromWho("Updated registered domain {$this->jobRecord->registeredDomainId} to OWNED status", $this->jobRecord->email));
    }

    private function updatePaymentNodeStatus(): void
    {
        PaymentNodeService::instance()->updateByRegisteredDomainId($this->jobRecord->registeredDomainId, 'status', PaymentNodeStatus::COMPLETED, $this->jobRecord->email);
    }

    private function sendNotification(): void
    {
        DomainNotificationService::instance()->sendDomainRedemptionSuccessNotif($this->jobRecord->name, $this->jobRecord->userId);
    }

    private function processRefund(string $reason): void
    {
        DomainNotificationService::instance()->sendDomainRenewalFailedNotif($this->jobRecord->name, $this->jobRecord->userId);

        $this->jobRecord->refundDetails['description'] = FeeType::TRANSACTION_TYPE[FeeType::REDEMPTION] ?? 'Domain Redemption';

        try {
            PaymentSummaryService::instance()->createRefund($this->jobRecord->refundDetails, $this->jobRecord->registeredDomainId, $this->jobRecord->userId);
            app(AuthLogger::class)->info($this->fromWho("Redemption refund processed: {$reason}", $this->jobRecord->email));
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho("Error processing redemption refund: {$e->getMessage()}", $this->jobRecord->email));
        }
    }
}
