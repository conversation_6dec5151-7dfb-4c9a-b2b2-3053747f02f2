<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use App\Modules\Setting\Constants\FeeType;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the base redemption fee to $200
        DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->update(['value' => 200]);

        echo "Updated redemption fee to $200\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('fees')
            ->where('type', FeeType::REDEMPTION)
            ->update(['value' => 100]);
    }
};
